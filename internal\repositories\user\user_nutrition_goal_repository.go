package user

import (
	"errors"
	"gorm.io/gorm"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
)

// IUserNutritionGoalRepo 定义了用户营养目标仓库需要实现的所有方法
type IUserNutritionGoalRepo interface {
	Create(goal *entities.UserNutritionGoals) error
	GetByID(id int64) (*entities.UserNutritionGoals, error)
	GetByUserID(userID int64) (*entities.UserNutritionGoals, error)
	Update(goal *entities.UserNutritionGoals) error
	Delete(id int64) error
	ExistsByUserID(userID int64) (bool, error)
}

// UserNutritionGoalRepository 用户营养目标仓储
type userNutritionGoalRepository struct {
	db *gorm.DB
}

// NewUserNutritionGoalRepository 创建用户营养目标仓储实例
func NewUserNutritionGoalRepository(db *gorm.DB) IUserNutritionGoalRepo {
	return &userNutritionGoalRepository{
		db: db,
	}
}

// 确保 userNutritionGoalRepository 实现了 IUserNutritionGoalRepo 接口
var _ IUserNutritionGoalRepo = &userNutritionGoalRepository{}

// Create 创建新的营养目标
func (r *userNutritionGoalRepository) Create(goal *entities.UserNutritionGoals) error {
	if err := r.db.Create(goal).Error; err != nil {
		// 检查是否是重复键错误（同一用户只能有一个营养目标）
		if errs.IsDuplicateKeyError(err) && errs.ContainsField(err.Error(), "user_id") {
			return errs.New(errs.CodeUserNutritionGoalExists, "nutrition goal already exists for user")
		}
		return errs.Wrap(errs.CodeDatabaseError, "failed to create nutrition goal", err)
	}
	return nil
}

// GetByID 根据ID获取营养目标
func (r *userNutritionGoalRepository) GetByID(id int64) (*entities.UserNutritionGoals, error) {
	var goal entities.UserNutritionGoals
	if err := r.db.Where("id = ?", id).First(&goal).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeUserNutritionGoalNotFound, "nutrition goal not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get nutrition goal by id", err)
	}
	return &goal, nil
}

// GetByUserID 根据用户ID获取营养目标
func (r *userNutritionGoalRepository) GetByUserID(userID int64) (*entities.UserNutritionGoals, error) {
	var goal entities.UserNutritionGoals
	if err := r.db.Where("user_id = ?", userID).First(&goal).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.New(errs.CodeUserNutritionGoalNotFound, "nutrition goal not found")
		}
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to get nutrition goal by user id", err)
	}
	return &goal, nil
}

// Update 更新营养目标
func (r *userNutritionGoalRepository) Update(goal *entities.UserNutritionGoals) error {
	if err := r.db.Save(goal).Error; err != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to update nutrition goal", err)
	}
	return nil
}

// Delete 删除营养目标
func (r *userNutritionGoalRepository) Delete(id int64) error {
	result := r.db.Delete(&entities.UserNutritionGoals{}, id)
	if result.Error != nil {
		return errs.Wrap(errs.CodeDatabaseError, "failed to delete nutrition goal", result.Error)
	}
	if result.RowsAffected == 0 {
		return errs.New(errs.CodeUserNutritionGoalNotFound, "nutrition goal not found")
	}
	return nil
}

// ExistsByUserID 检查用户是否已有营养目标
func (r *userNutritionGoalRepository) ExistsByUserID(userID int64) (bool, error) {
	var count int64
	if err := r.db.Model(&entities.UserNutritionGoals{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return false, errs.Wrap(errs.CodeDatabaseError, "failed to check nutrition goal exists", err)
	}
	return count > 0, nil
}


