package controllers

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// NutritionController 营养分析控制器，处理营养相关的HTTP请求
type NutritionController struct {
	nutritionStatService    service.INutritionStatService
	nutritionAdviceService  service.INutritionAdviceService
	healthReportService     service.IHealthReportService
}

// NewNutritionController 创建一个新的 NutritionController 实例
func NewNutritionController(
	nutritionStatService service.INutritionStatService,
	nutritionAdviceService service.INutritionAdviceService,
	healthReportService service.IHealthReportService,
) *NutritionController {
	return &NutritionController{
		nutritionStatService:   nutritionStatService,
		nutritionAdviceService: nutritionAdviceService,
		healthReportService:    healthReportService,
	}
}

// bindQueryAndValidate 统一处理查询参数绑定和验证
func (nc *NutritionController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		c.Error(errs.New(errs.CodeNutritionInvalidParams, "invalid query parameters"))
		return false
	}
	return true
}

// getCurrentUserID 获取当前用户ID
func (nc *NutritionController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.Error(errs.New(errs.CodeAuthTokenInvalid, "authentication required"))
		return 0, false
	}
	return userID, true
}

// GetDailyNutrition 获取每日营养摄入统计
// GET /api/nutrition/daily
func (nc *NutritionController) GetDailyNutrition(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := nc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionStatReq
	if !nc.bindQueryAndValidate(c, &req) {
		return
	}

	// 如果没有提供日期，使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 调用服务层获取营养统计数据
	nutritionStat, err := nc.nutritionStatService.GetDailyNutritionStat(c.Request.Context(), userID, date)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, nutritionStat)
}

// GetNutritionTrend 获取营养摄入趋势
// GET /api/nutrition/trend
func (nc *NutritionController) GetNutritionTrend(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := nc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionTrendReq
	if !nc.bindQueryAndValidate(c, &req) {
		return
	}

	// 添加调试日志
	fmt.Printf("DEBUG: NutritionTrend request - Type=%s, StartDate=%s, EndDate=%s\n", req.Type, req.StartDate, req.EndDate)

	// 根据type参数或设置默认日期范围
	if req.StartDate == "" || req.EndDate == "" {
		endDate := time.Now()
		var startDate time.Time

		// 根据type参数设置日期范围
		switch req.Type {
		case "week":
			startDate = endDate.AddDate(0, 0, -6) // 最近一周
		case "month":
			startDate = endDate.AddDate(0, 0, -29) // 最近一个月
		default:
			startDate = endDate.AddDate(0, 0, -29) // 默认最近30天
		}

		if req.StartDate == "" {
			req.StartDate = startDate.Format("2006-01-02")
		}
		if req.EndDate == "" {
			req.EndDate = endDate.Format("2006-01-02")
		}

		// 添加调试日志
		fmt.Printf("DEBUG: Calculated date range - Type=%s, StartDate=%s, EndDate=%s\n", req.Type, req.StartDate, req.EndDate)
	}

	// 限制最大日期范围为90天 - 使用本地时区
	startDate, err := time.ParseInLocation("2006-01-02", req.StartDate, time.Local)
	if err != nil {
		c.Error(errs.New(errs.CodeNutritionInvalidParams, "invalid start date format"))
		return
	}
	endDate, err := time.ParseInLocation("2006-01-02", req.EndDate, time.Local)
	if err != nil {
		c.Error(errs.New(errs.CodeNutritionInvalidParams, "invalid end date format"))
		return
	}

	// 检查日期范围
	if endDate.Sub(startDate).Hours() > 90*24 {
		endDate = startDate.AddDate(0, 0, 90)
		req.EndDate = endDate.Format("2006-01-02")
	}

	// 调用服务层获取营养趋势数据
	trendData, err := nc.nutritionStatService.GetNutritionTrend(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, trendData)
}

// GetNutritionDetails 获取营养摄入详情
// GET /api/nutrition/details
func (nc *NutritionController) GetNutritionDetails(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := nc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionStatReq
	if !nc.bindQueryAndValidate(c, &req) {
		return
	}

	// 如果没有提供日期，使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 调用服务层获取营养详情数据
	detailList, err := nc.nutritionStatService.GetNutritionDetails(c.Request.Context(), userID, date)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, detailList)
}

// GetNutritionAdvice 获取营养建议
// GET /api/nutrition/advice
func (nc *NutritionController) GetNutritionAdvice(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := nc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionAdviceReq
	if !nc.bindQueryAndValidate(c, &req) {
		return
	}

	// 如果没有提供日期，使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 调用服务层获取营养建议
	adviceList, err := nc.nutritionAdviceService.GetNutritionAdvice(c.Request.Context(), userID, date)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, adviceList)
}

// GetHealthReport 获取健康报告
// GET /api/health/report
func (nc *NutritionController) GetHealthReport(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := nc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.HealthReportReq
	if !nc.bindQueryAndValidate(c, &req) {
		return
	}

	// 如果没有提供日期，使用当天
	date := req.Date
	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 调用服务层获取健康报告
	healthReport, err := nc.healthReportService.GetHealthReport(c.Request.Context(), userID, date)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, healthReport)
}
