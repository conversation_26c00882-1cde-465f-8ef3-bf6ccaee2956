package service

import (
	"context"
	v1 "shikeyinxiang/api/v1"
)

// IDietRecordService 饮食记录服务接口，定义了所有与饮食记录管理相关的业务能力
type IDietRecordService interface {
	// CreateDietRecord 创建饮食记录
	CreateDietRecord(ctx context.Context, userID int64, req *v1.DietRecordCreateReq) (bool, error)

	// GetDietRecord 获取饮食记录详情
	GetDietRecord(ctx context.Context, userID int64, id int64) (*v1.DietRecordResponse, error)

	// DeleteDietRecord 删除饮食记录
	DeleteDietRecord(ctx context.Context, userID int64, id int64) error

	// BatchDeleteDietRecords 批量删除饮食记录
	BatchDeleteDietRecords(ctx context.Context, userID int64, req *v1.DietRecordBatchDeleteReq) error

	// ListDietRecords 获取饮食记录列表（支持分页、筛选）
	ListDietRecords(ctx context.Context, userID int64, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error)

	// GetDietRecordsByDate 获取指定日期的饮食记录
	GetDietRecordsByDate(ctx context.Context, userID int64, date string) ([]*v1.DietRecordResponse, error)

	// 批量查询方法（用于营养统计服务）
	// GetBatchDietRecordsForNutritionStat 批量获取多个用户在指定日期范围内的饮食记录
	// 专门用于营养统计的聚合查询，避免多次查询
	// 返回格式：map[userID]map[date][]*DietRecordResponse
	GetBatchDietRecordsForNutritionStat(ctx context.Context, userIDs []int64, startDate, endDate string) (map[int64]map[string][]*v1.DietRecordResponse, error)

	// FindActiveUserIdsByDate 获取指定日期有饮食记录的活跃用户ID列表
	FindActiveUserIdsByDate(ctx context.Context, date string) ([]int64, error)

	// FindActiveUserIdsByDateRange 获取指定日期范围内有饮食记录的活跃用户ID列表
	FindActiveUserIdsByDateRange(ctx context.Context, startDate, endDate string) ([]int64, error)

	// CountDietRecordsByDate 统计指定日期的饮食记录数量
	CountDietRecordsByDate(ctx context.Context, date string) (int, error)

	// GetPopularFoodsByPeriod 根据时间周期获取热门食物统计
	GetPopularFoodsByPeriod(ctx context.Context, period string, limit int) ([]map[string]interface{}, error)

	// 管理端专用方法
	// GetDietRecordDetail 获取饮食记录详情（不验证用户权限，管理端使用）
	GetDietRecordDetail(ctx context.Context, id int64) (*v1.DietRecordResponse, error)

	// ListAllUsersDietRecords 获取所有用户的饮食记录列表（管理端使用）
	ListAllUsersDietRecords(ctx context.Context, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error)

	// DeleteDietRecordAdmin 删除饮食记录（管理端，不验证用户权限）
	DeleteDietRecordAdmin(ctx context.Context, id int64) error

	// BatchDeleteDietRecordsAdmin 批量删除饮食记录（管理端，不验证用户权限）
	BatchDeleteDietRecordsAdmin(ctx context.Context, req *v1.DietRecordBatchDeleteReq) error

	// GetNutritionStatsAdmin 获取营养统计（管理端，支持跨用户统计）
	GetNutritionStatsAdmin(ctx context.Context, req *v1.NutritionStatsQueryReq) (*v1.NutritionStatsResponse, error)
}


