package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// DietRecordAdminController 饮食记录管理端控制器，处理管理员的饮食记录相关HTTP请求
type DietRecordAdminController struct {
	dietRecordAdminSvc service.IDietRecordAdminService
}

// NewDietRecordAdminController 创建一个新的 DietRecordAdminController 实例
func NewDietRecordAdminController(dietRecordAdminSvc service.IDietRecordAdminService) *DietRecordAdminController {
	return &DietRecordAdminController{
		dietRecordAdminSvc: dietRecordAdminSvc,
	}
}

// bindJSONAndValidate 统一处理JSON参数绑定和验证
func (dac *DietRecordAdminController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid request parameters"))
		return false
	}
	return true
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (dac *DietRecordAdminController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid query parameters"))
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (dac *DietRecordAdminController) parseIDParam(c *gin.Context, paramName string) (int64, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		c.Error(errs.New(errs.CodeDietInvalidParams, "missing id parameter"))
		return 0, false
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid id parameter"))
		return 0, false
	}

	return id, true
}

// GetDietRecord 获取饮食记录详情（管理端）
func (dac *DietRecordAdminController) GetDietRecord(c *gin.Context) {
	// 解析ID参数
	id, ok := dac.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层
	res, err := dac.dietRecordAdminSvc.GetDietRecord(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetDietRecordList 获取饮食记录列表（管理端，支持跨用户查询）
func (dac *DietRecordAdminController) GetDietRecordList(c *gin.Context) {
	// 绑定查询参数
	var req v1.DietRecordQueryReq
	if !dac.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	res, err := dac.dietRecordAdminSvc.ListDietRecords(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// DeleteDietRecord 删除饮食记录（管理端）
func (dac *DietRecordAdminController) DeleteDietRecord(c *gin.Context) {
	// 解析ID参数
	id, ok := dac.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层
	err := dac.dietRecordAdminSvc.DeleteDietRecord(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, nil)
}

// BatchDeleteDietRecords 批量删除饮食记录（管理端）
func (dac *DietRecordAdminController) BatchDeleteDietRecords(c *gin.Context) {
	// 绑定请求参数
	var req v1.DietRecordBatchDeleteReq
	if !dac.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层
	err := dac.dietRecordAdminSvc.BatchDeleteDietRecords(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, nil)
}

// GetNutritionStats 获取营养统计（管理端，支持跨用户统计）
func (dac *DietRecordAdminController) GetNutritionStats(c *gin.Context) {
	// 绑定查询参数
	var req v1.NutritionStatsQueryReq
	if !dac.bindQueryAndValidate(c, &req) {
		return
	}

	// 调用服务层
	res, err := dac.dietRecordAdminSvc.GetNutritionStats(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetUserNutritionStats 获取指定用户的营养统计（管理端）
func (dac *DietRecordAdminController) GetUserNutritionStats(c *gin.Context) {
	// 解析用户ID参数
	userID, ok := dac.parseIDParam(c, "userId")
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionStatsQueryReq
	if !dac.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置用户ID
	req.UserID = &userID

	// 调用服务层
	res, err := dac.dietRecordAdminSvc.GetNutritionStats(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetSystemStats 获取系统级营养统计（管理端）
func (dac *DietRecordAdminController) GetSystemStats(c *gin.Context) {
	// 绑定查询参数
	var req v1.NutritionStatsQueryReq
	if !dac.bindQueryAndValidate(c, &req) {
		return
	}

	// 系统级统计需要特殊处理
	// 这里简化处理，实际应该有专门的系统统计方法
	if req.UserID == nil {
		c.Error(errs.New(errs.CodeDietInvalidParams, "userID parameter is required"))
		return
	}

	// 调用服务层
	res, err := dac.dietRecordAdminSvc.GetNutritionStats(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// ExportDietRecords 导出饮食记录数据（管理端）
func (dac *DietRecordAdminController) ExportDietRecords(c *gin.Context) {
	// 绑定查询参数
	var req v1.DietRecordQueryReq
	if !dac.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置大的分页限制用于导出
	req.Size = 10000 // 导出时设置较大的限制

	// 调用服务层获取数据
	res, err := dac.dietRecordAdminSvc.ListDietRecords(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 简化处理：返回JSON格式的数据
	// 实际应用中可以导出为CSV或Excel格式
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=diet_records_export.json")
	response.Success(c, res.Records)
}
