package auth

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/repositories/user"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/jwt"
	"shikeyinxiang/internal/pkg"
	"shikeyinxiang/internal/service"
)

// WechatLoginResponse 微信登录响应
type WechatLoginResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionID    string `json:"unionid,omitempty"`
	ErrCode    int    `json:"errcode,omitempty"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

// authLogic 认证业务逻辑实现
type authLogic struct {
	userRepo     user.IUserRepo
	wechatConfig *config.WechatConfig
	httpClient   *http.Client
}

// NewAuth 创建认证业务逻辑实例
func NewAuth(userRepo user.IUserRepo, wechatConfig *config.WechatConfig) service.IAuth {
	return &authLogic{
		userRepo:     userRepo,
		wechatConfig: wechatConfig,
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// AdminLogin 管理员登录
func (a *authLogic) AdminLogin(ctx context.Context, req *v1.AdminLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Username == "" {
		return nil, errs.New(errs.CodeAuthInvalidParams, "username is required")
	}

	// 根据用户名查找用户
	user, err := a.userRepo.GetByUsername(req.Username)
	if err != nil {
		// 检查是否是用户未找到错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && codeErr.Code == errs.CodeUserNotFound {
			return nil, errs.New(errs.CodeAuthInvalidCredentials, "invalid credentials")
		}
		return nil, errs.Wrap(errs.CodeAuthLoginFailed, "failed to get user for admin login", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, errs.New(errs.CodeAuthInvalidCredentials, "invalid credentials")
	}

	// 验证用户角色
	if user.Role != consts.RoleAdmin {
		return nil, errs.New(errs.CodeAuthInsufficientPermissions, "insufficient permissions")
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, errs.New(errs.CodeAuthAccountDisabled, "account disabled")
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthTokenGenerationFailed, "failed to generate token", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// UserLogin 普通用户登录
func (a *authLogic) UserLogin(ctx context.Context, req *v1.UserLoginReq) (*v1.LoginRes, error) {
	// 参数验证
	if req.Email == "" {
		return nil, errs.New(errs.CodeAuthInvalidParams, "email is required")
	}

	// 根据邮箱查找用户
	user, err := a.userRepo.GetByEmail(req.Email)
	if err != nil {
		// 检查是否是用户未找到错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && codeErr.Code == errs.CodeUserNotFound {
			return nil, errs.New(errs.CodeAuthInvalidCredentials, "invalid credentials")
		}
		return nil, errs.Wrap(errs.CodeAuthLoginFailed, "failed to get user for login", err)
	}

	// 验证密码
	if !pkg.VerifyPassword(user.Password, req.Password) {
		return nil, errs.New(errs.CodeAuthInvalidCredentials, "invalid credentials")
	}

	// 验证用户角色
	if user.Role != consts.RoleUser {
		return nil, errs.New(errs.CodeAuthInsufficientPermissions, "insufficient permissions")
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, errs.New(errs.CodeAuthAccountDisabled, "account disabled")
	}

	// 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthTokenGenerationFailed, "failed to generate token", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// WechatLogin 微信登录
func (a *authLogic) WechatLogin(ctx context.Context, req *v1.WechatLoginReq) (*v1.LoginRes, error) {
	// 1. 使用code向微信服务器获取openid
	wechatResp, err := a.getOpenIDByCode(req.Code)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthExternalServiceError, "wechat service error", err)
	}

	// 2. 根据openid查找用户
	user, err := a.userRepo.GetByOpenID(wechatResp.OpenID)
	if err != nil {
		// 检查是否是用户未找到错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && codeErr.Code == errs.CodeUserNotFound {
			// 用户不存在，创建新用户
			user, err = a.createWechatUser(wechatResp.OpenID)
			if err != nil {
				return nil, errs.Wrap(errs.CodeAuthLoginFailed, "failed to create wechat user", err)
			}
		} else {
			return nil, errs.Wrap(errs.CodeAuthLoginFailed, "failed to get user by openid", err)
		}
	}

	// 检查用户状态
	if user.Status != consts.UserStatusActive {
		return nil, errs.New(errs.CodeAuthAccountDisabled, "account disabled")
	}

	// 3. 生成JWT令牌
	token, err := jwt.GenerateToken(user.ID, user.Username, user.Role)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthTokenGenerationFailed, "failed to generate token", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.LoginRes{
		Token:    token,
		UserInfo: userInfo,
	}, nil
}

// Register 用户注册
func (a *authLogic) Register(ctx context.Context, req *v1.RegisterReq) (*v1.RegisterRes, error) {
	// 检查用户名是否已存在
	exists, err := a.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to check username exists", err)
	}
	if exists {
		return nil, errs.New(errs.CodeUserAlreadyExists, "username already exists")
	}

	// 检查邮箱是否已存在
	exists, err = a.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		return nil, errs.Wrap(errs.CodeDatabaseError, "failed to check email exists", err)
	}
	if exists {
		return nil, errs.New(errs.CodeUserEmailExists, "email already exists")
	}

	// 加密密码
	hashedPassword, err := pkg.HashPassword(req.Password)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthPasswordHashFailed, "failed to hash password", err)
	}

	// 创建用户实体
	user := entities.NewUser(req.Username, req.Email, hashedPassword, consts.RoleUser)

	// 保存用户
	if err := a.userRepo.Create(user); err != nil {
		// 检查是否是用户已存在错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && (codeErr.Code == errs.CodeUserAlreadyExists || codeErr.Code == errs.CodeUserEmailExists) {
			return nil, err // 直接透传 CodeError
		}
		return nil, errs.Wrap(errs.CodeUserCreateFailed, "failed to create user", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return &v1.RegisterRes{
		User: userInfo,
	}, nil
}

// Logout 用户登出
func (a *authLogic) Logout(ctx context.Context, token string) error {
	// 将token加入黑名单
	if err := jwt.BlacklistToken(token); err != nil {
		return errs.Wrap(errs.CodeAuthTokenBlacklistFailed, "failed to blacklist token", err)
	}
	return nil
}

// ChangePassword 修改密码
func (a *authLogic) ChangePassword(ctx context.Context, req *v1.ChangePasswordReq, userID int64) (bool, error) {
	// 获取当前用户信息
	user, err := a.userRepo.GetByID(userID)
	if err != nil {
		// 检查是否是用户未找到错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && codeErr.Code == errs.CodeUserNotFound {
			return false, errs.New(errs.CodeUserNotFound, "user not found")
		}
		return false, errs.Wrap(errs.CodeUserQueryFailed, "failed to get user for password change", err)
	}

	// 验证旧密码
	if !pkg.VerifyPassword(user.Password, req.OldPassword) {
		return false, errs.New(errs.CodeAuthInvalidCredentials, "invalid old password")
	}

	// 加密新密码
	hashedPassword, err := pkg.HashPassword(req.NewPassword)
	if err != nil {
		return false, errs.Wrap(errs.CodeAuthPasswordHashFailed, "failed to hash new password", err)
	}

	// 更新密码
	if err := a.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return false, errs.Wrap(errs.CodeUserUpdateFailed, "failed to update password", err)
	}

	return true, nil
}

// GetCurrentUser 获取当前用户信息
func (a *authLogic) GetCurrentUser(ctx context.Context, userId int64) (*v1.UserInfo, error) {
	user, err := a.userRepo.GetByID(userId)
	if err != nil {
		// 检查是否是用户未找到错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && codeErr.Code == errs.CodeUserNotFound {
			return nil, errs.New(errs.CodeUserNotFound, "user not found")
		}
		return nil, errs.Wrap(errs.CodeUserQueryFailed, "failed to get current user", err)
	}

	// 构造响应
	userInfo := &v1.UserInfo{
		ID:         user.ID,
		Username:   user.Username,
		Email:      user.Email,
		Role:       user.Role,
		Status:     user.Status,
		AvatarURL:  user.AvatarURL,
		CreateTime: user.CreateTime,
	}

	return userInfo, nil
}

// createWechatUser 创建微信用户
func (a *authLogic) createWechatUser(openID string) (*entities.User, error) {
	// 生成随机用户名
	username := fmt.Sprintf("wx_%s", openID[:8])

	// 生成随机密码
	randomPassword := fmt.Sprintf("%d", time.Now().UnixNano())
	hashedPassword, err := pkg.HashPassword(randomPassword)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthPasswordHashFailed, "failed to hash password", err)
	}

	// 设置占位邮箱
	email := fmt.Sprintf("%<EMAIL>", openID)

	// 创建用户实体
	user := &entities.User{
		Username: username,
		Email:    email,
		Password: hashedPassword,
		Role:     consts.RoleUser,
		Status:   consts.UserStatusActive,
		OpenID:   openID,
	}

	// 保存用户
	if err := a.userRepo.Create(user); err != nil {
		// 检查是否是用户已存在错误
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) && (codeErr.Code == errs.CodeUserAlreadyExists || codeErr.Code == errs.CodeUserEmailExists) {
			return nil, err // 直接透传 CodeError
		}
		return nil, errs.Wrap(errs.CodeUserCreateFailed, "failed to create wechat user", err)
	}

	return user, nil
}

// getOpenIDByCode 通过code获取用户openid
func (a *authLogic) getOpenIDByCode(code string) (*WechatLoginResponse, error) {
	// 构建请求URL
	requestURL := fmt.Sprintf("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		a.wechatConfig.LoginURL,
		a.wechatConfig.AppID,
		a.wechatConfig.Secret,
		url.QueryEscape(code))

	// 发送HTTP请求
	resp, err := a.httpClient.Get(requestURL)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthExternalServiceError, "failed to request wechat api", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errs.Wrap(errs.CodeAuthExternalServiceError, "failed to read wechat response", err)
	}

	// 解析JSON响应
	var wechatResp WechatLoginResponse
	if err := json.Unmarshal(body, &wechatResp); err != nil {
		return nil, errs.Wrap(errs.CodeAuthExternalServiceError, "failed to parse wechat response", err)
	}

	// 检查微信返回的错误
	if wechatResp.ErrCode != 0 {
		return nil, errs.New(errs.CodeAuthExternalServiceError, fmt.Sprintf("wechat api error: code=%d, msg=%s", wechatResp.ErrCode, wechatResp.ErrMsg))
	}

	// 检查必要字段
	if wechatResp.OpenID == "" {
		return nil, errs.New(errs.CodeAuthExternalServiceError, "wechat response missing openid")
	}

	return &wechatResp, nil
}
