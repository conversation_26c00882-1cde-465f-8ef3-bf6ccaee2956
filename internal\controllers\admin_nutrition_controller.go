package controllers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// AdminNutritionController 后台管理-营养分析控制器
type AdminNutritionController struct {
	nutritionStatService   service.INutritionStatService
	nutritionAdviceService service.INutritionAdviceService
}

// NewAdminNutritionController 创建一个新的 AdminNutritionController 实例
func NewAdminNutritionController(
	nutritionStatService service.INutritionStatService,
	nutritionAdviceService service.INutritionAdviceService,
) *AdminNutritionController {
	return &AdminNutritionController{
		nutritionStatService:   nutritionStatService,
		nutritionAdviceService: nutritionAdviceService,
	}
}

// bindQueryAndValidate 统一处理查询参数绑定和验证
func (anc *AdminNutritionController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid query parameters"))
		return false
	}
	return true
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (anc *AdminNutritionController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid JSON parameters"))
		return false
	}
	return true
}

// GetAllNutritionTrend 获取全体用户营养趋势
// GET /api/admin/nutrition/trend
func (anc *AdminNutritionController) GetAllNutritionTrend(c *gin.Context) {
	// 获取查询参数
	period := c.DefaultQuery("period", "month")

	// 验证period参数
	validPeriods := map[string]bool{"week": true, "month": true, "year": true}
	if !validPeriods[period] {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid period parameter"))
		return
	}

	// 调用服务层获取全体用户营养趋势
	trendData, err := anc.nutritionStatService.GetAllNutritionTrend(c.Request.Context(), period)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, trendData)
}

// GetNutritionComplianceRate 获取营养达标率
// GET /api/admin/nutrition/compliance-rate
func (anc *AdminNutritionController) GetNutritionComplianceRate(c *gin.Context) {
	// 获取日期参数，默认为当天
	dateStr := c.DefaultQuery("date", time.Now().Format("2006-01-02"))

	// 调用服务层计算营养达标率
	complianceRate, err := anc.nutritionStatService.CalculateNutritionComplianceRate(c.Request.Context(), dateStr)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, complianceRate)
}

// GetAllAdvices 获取所有营养建议
// GET /api/admin/nutrition/advice
func (anc *AdminNutritionController) GetAllAdvices(c *gin.Context) {
	// 绑定查询参数
	var req v1.NutritionAdviceQueryReq
	if !anc.bindQueryAndValidate(c, &req) {
		return
	}

	// 调用服务层获取营养建议列表
	adviceList, err := anc.nutritionAdviceService.ListNutritionAdvice(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, adviceList)
}

// CreateAdvice 创建营养建议
// POST /api/admin/nutrition/advice
func (anc *AdminNutritionController) CreateAdvice(c *gin.Context) {
	// 绑定JSON参数
	var req v1.NutritionAdviceManageReq
	if !anc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层创建营养建议
	createdAdvice, err := anc.nutritionAdviceService.CreateNutritionAdvice(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, createdAdvice)
}

// UpdateAdvice 更新营养建议
// PUT /api/admin/nutrition/advice/:id
func (anc *AdminNutritionController) UpdateAdvice(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid id parameter"))
		return
	}

	// 绑定JSON参数
	var req v1.NutritionAdviceUpdateReq
	if !anc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置ID
	req.ID = id

	// 调用服务层更新营养建议
	updatedAdvice, err := anc.nutritionAdviceService.UpdateNutritionAdvice(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, updatedAdvice)
}

// DeleteAdvice 删除营养建议
// DELETE /api/admin/nutrition/advice/:id
func (anc *AdminNutritionController) DeleteAdvice(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid id parameter"))
		return
	}

	// 调用服务层删除营养建议
	err = anc.nutritionAdviceService.DeleteNutritionAdvice(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, true)
}

// GetAdviceById 根据ID获取营养建议
// GET /api/admin/nutrition/advice/:id
func (anc *AdminNutritionController) GetAdviceById(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid id parameter"))
		return
	}

	// 调用服务层获取营养建议
	advice, err := anc.nutritionAdviceService.GetNutritionAdviceByID(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, advice)
}

// GetAdvicesByConditionType 根据条件类型获取营养建议
// GET /api/admin/nutrition/advice/condition/:conditionType
func (anc *AdminNutritionController) GetAdvicesByConditionType(c *gin.Context) {
	// 获取路径参数
	conditionType := c.Param("conditionType")
	if conditionType == "" {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "conditionType parameter is required"))
		return
	}

	// 调用服务层获取营养建议列表
	adviceList, err := anc.nutritionAdviceService.GetAdvicesByConditionType(c.Request.Context(), conditionType)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, adviceList)
}

// UpdateAdviceStatus 更新营养建议状态
// PUT /api/admin/nutrition/advice/:id/status
func (anc *AdminNutritionController) UpdateAdviceStatus(c *gin.Context) {
	// 获取路径参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid id parameter"))
		return
	}

	// 绑定JSON参数
	var req v1.NutritionAdviceStatusUpdateReq
	if !anc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层更新营养建议状态
	err = anc.nutritionAdviceService.UpdateAdviceStatus(c.Request.Context(), id, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, true)
}
