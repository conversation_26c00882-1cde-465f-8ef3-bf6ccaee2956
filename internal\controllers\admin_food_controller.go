package controllers

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// AdminFoodController 管理端食物控制器，处理管理员的食物和分类管理HTTP请求
type AdminFoodController struct {
	foodSvc     service.IFoodService
	categorySvc service.IFoodCategoryService
}

// NewAdminFoodController 创建一个新的 AdminFoodController 实例
func NewAdminFoodController(foodSvc service.IFoodService, categorySvc service.IFoodCategoryService) *AdminFoodController {
	return &AdminFoodController{
		foodSvc:     foodSvc,
		categorySvc: categorySvc,
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (afc *AdminFoodController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid request parameters"))
		return false
	}
	return true
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (afc *AdminFoodController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid query parameters"))
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (afc *AdminFoodController) parseIDParam(c *gin.Context, paramName string) (int, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "missing id parameter"))
		return 0, false
	}

	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid id parameter"))
		return 0, false
	}

	return id, true
}

// getCurrentUserID 获取当前用户ID
func (afc *AdminFoodController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.Error(errs.New(errs.CodeAuthTokenInvalid, "authentication required"))
		return 0, false
	}
	return userID, true
}

// ===== 食物管理方法 =====

// CreateFood 创建食物
func (afc *AdminFoodController) CreateFood(c *gin.Context) {
	var req v1.FoodCreateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.foodSvc.CreateFood(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateFood 更新食物
func (afc *AdminFoodController) UpdateFood(c *gin.Context) {
	// 获取路径参数中的食物ID
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	var req v1.FoodUpdateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置ID到请求对象中（与Java版本行为一致）
	req.ID = id

	res, err := afc.foodSvc.UpdateFood(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteFood 删除食物
func (afc *AdminFoodController) DeleteFood(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	err := afc.foodSvc.DeleteFood(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// GetFoodList 获取食物列表（管理端）
func (afc *AdminFoodController) GetFoodList(c *gin.Context) {
	var req v1.FoodQueryReq
	if !afc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := afc.foodSvc.ListFoods(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetFoodDetail 获取食物详情（管理端）
func (afc *AdminFoodController) GetFoodDetail(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := afc.foodSvc.GetFood(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// ===== 分类管理方法 =====

// CreateCategory 创建食物分类（与Java版本保持一致，使用FoodCategoryResponse作为请求体）
func (afc *AdminFoodController) CreateCategory(c *gin.Context) {
	var req v1.FoodCategoryResponse
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	// 转换为创建请求格式
	createReq := &v1.FoodCategoryCreateReq{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		SortOrder:   req.SortOrder,
	}

	res, err := afc.categorySvc.CreateCategory(c.Request.Context(), createReq)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateCategory 更新食物分类
func (afc *AdminFoodController) UpdateCategory(c *gin.Context) {
	// 获取路径参数中的分类ID
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	var req v1.FoodCategoryUpdateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	// 设置ID到请求对象中（与Java版本行为一致）
	req.ID = id

	res, err := afc.categorySvc.UpdateCategory(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteCategory 删除食物分类
func (afc *AdminFoodController) DeleteCategory(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	err := afc.categorySvc.DeleteCategory(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// GetCategoryDetail 获取食物分类详情（管理端）
func (afc *AdminFoodController) GetCategoryDetail(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := afc.categorySvc.GetCategory(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetAllCategories 获取所有食物分类（管理端）- 与Java版本保持一致
func (afc *AdminFoodController) GetAllCategories(c *gin.Context) {
	res, err := afc.categorySvc.GetAllCategories(c.Request.Context())
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetCategoryPage 分页查询食物分类（管理端）- 与Java版本保持一致
func (afc *AdminFoodController) GetCategoryPage(c *gin.Context) {
	// 获取分页参数
	currentStr := c.DefaultQuery("current", "1")
	sizeStr := c.DefaultQuery("size", "15")

	current, err := strconv.Atoi(currentStr)
	if err != nil || current <= 0 {
		current = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size <= 0 {
		size = 15
	}

	// 构建查询请求
	req := &v1.FoodCategoryQueryReq{
		Current: current,
		Size:    size,
	}

	res, err := afc.categorySvc.ListCategories(c.Request.Context(), req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式（与Java版本保持一致）
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// UpdateCategorySortOrder 更新分类排序顺序
func (afc *AdminFoodController) UpdateCategorySortOrder(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	var req struct {
		SortOrder int `json:"sortOrder" binding:"required,min=0"`
	}
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	err := afc.categorySvc.UpdateCategorySortOrder(c.Request.Context(), id, req.SortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// ===== 批量操作方法 =====

// ImportFoods 批量导入食物数据（管理端）- 与Java版本保持一致
func (afc *AdminFoodController) ImportFoods(c *gin.Context) {
	var req v1.FoodImportReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.foodSvc.BatchImportFoods(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// ===== 图片上传方法 =====

// GetUploadImageUrl 生成食物图片上传URL
// 与Java版本保持一致，使用query参数：foodId和contentType
func (afc *AdminFoodController) GetUploadImageUrl(c *gin.Context) {
	// 获取query参数（与Java版本一致）
	foodIdStr := c.Query("foodId")
	contentType := c.Query("contentType")

	// 参数验证
	if foodIdStr == "" {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "foodId parameter is required"))
		return
	}
	if contentType == "" {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "contentType parameter is required"))
		return
	}

	// 转换foodId为int64
	foodId, err := strconv.ParseInt(foodIdStr, 10, 64)
	if err != nil || foodId <= 0 {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid foodId parameter"))
		return
	}

	// 调用食物服务生成上传URL
	uploadResult, err := afc.foodSvc.GenerateImageUploadURL(c.Request.Context(), foodId, contentType)
	if err != nil {
		// 记录详细错误信息用于调试
		fmt.Printf("Error generating upload URL: %v\n", err)
		c.Error(errs.Wrap(errs.CodeFileGenerateURLError, "failed to generate upload URL", err))
		return
	}

	// 直接返回预签名URL字符串（与Java版本ApiResponse<String>格式一致）
	response.Success(c, uploadResult)
}

// GetFoodImageUrl 获取食物图片下载URL
func (afc *AdminFoodController) GetFoodImageUrl(c *gin.Context) {
	// 获取路径参数中的食物ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid food id"))
		return
	}

	// 调用食物服务获取图片下载URL
	imageUrl, err := afc.foodSvc.GetFoodImageURL(c.Request.Context(), id)
	if err != nil {
		fmt.Printf("Error getting food image URL: %v\n", err)
		c.Error(errs.Wrap(errs.CodeFileGenerateURLError, "failed to get food image URL", err))
		return
	}

	// 返回图片下载URL
	response.Success(c, imageUrl)
}

// UpdateFoodImageUrl 更新食物图片URL
func (afc *AdminFoodController) UpdateFoodImageUrl(c *gin.Context) {
	// 获取路径参数中的食物ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid food id"))
		return
	}

	// 绑定请求体
	var req struct {
		ImageURL string `json:"imageUrl" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.Error(errs.New(errs.CodeSystemInvalidParams, "invalid request body"))
		return
	}

	// 调用食物服务更新图片URL
	err = afc.foodSvc.UpdateFoodImageURL(c.Request.Context(), id, req.ImageURL)
	if err != nil {
		c.Error(errs.Wrap(errs.CodeInternal, "failed to update food image URL", err))
		return
	}

	// 返回成功结果（与Java版本ApiResponse<Boolean>格式一致）
	response.Success(c, true)
}
