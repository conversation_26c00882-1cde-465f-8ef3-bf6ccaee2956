package diet

import (
	"context"
	"errors"
	"fmt"
	"shikeyinxiang/internal/repositories/diet"
	"shikeyinxiang/internal/repositories/food"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// dietRecordAdminLogic 饮食记录管理端业务逻辑实现
type dietRecordAdminLogic struct {
	dietRecordRepo     diet.IDietRecordRepo
	dietRecordFoodRepo diet.IDietRecordFoodRepo
	foodRepo           food.IFoodRepo
}

// NewDietRecordAdminLogic 创建饮食记录管理端业务逻辑实例
func NewDietRecordAdminLogic(
	dietRecordRepo diet.IDietRecordRepo,
	dietRecordFoodRepo diet.IDietRecordFoodRepo,
	foodRepo food.IFoodRepo,
) service.IDietRecordAdminService {
	return &dietRecordAdminLogic{
		dietRecordRepo:     dietRecordRepo,
		dietRecordFoodRepo: dietRecordFoodRepo,
		foodRepo:           foodRepo,
	}
}

// 确保 dietRecordAdminLogic 实现了 IDietRecordAdminService 接口
var _ service.IDietRecordAdminService = &dietRecordAdminLogic{}

// GetDietRecord 获取饮食记录详情（管理端）
func (d *dietRecordAdminLogic) GetDietRecord(ctx context.Context, id int64) (*v1.DietRecordResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeDietInvalidParams, "id must be positive")
	}

	// 获取饮食记录（管理端不需要权限检查）
	dietRecord, err := d.dietRecordRepo.GetByIDWithFoods(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietRecordNotFound, "failed to get diet record", err)
	}

	return d.convertToDietRecordResponse(dietRecord), nil
}

// ListDietRecords 获取饮食记录列表（管理端，支持跨用户查询）
func (d *dietRecordAdminLogic) ListDietRecords(ctx context.Context, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error) {
	// 设置默认分页参数
	page := 1
	size := 10
	if req.Current > 0 {
		page = req.Current
	}
	if req.Size > 0 {
		size = req.Size
	}

	offset := (page - 1) * size

	// 解析日期范围 - 使用本地时区避免时区转换问题
	var startDate, endDate *time.Time
	if req.StartDate != nil {
		if parsed, err := time.ParseInLocation("2006-01-02", *req.StartDate, time.Local); err != nil {
			return nil, errs.New(errs.CodeDietInvalidParams, "invalid startDate format, expected YYYY-MM-DD")
		} else {
			startDate = &parsed
		}
	}
	if req.EndDate != nil {
		if parsed, err := time.ParseInLocation("2006-01-02", *req.EndDate, time.Local); err != nil {
			return nil, errs.New(errs.CodeDietInvalidParams, "invalid endDate format, expected YYYY-MM-DD")
		} else {
			endDate = &parsed
		}
	}

	// 验证日期范围
	if startDate != nil && endDate != nil && startDate.After(*endDate) {
		return nil, errs.New(errs.CodeDietInvalidDateRange, fmt.Sprintf("startDate %s cannot be after endDate %s", *req.StartDate, *req.EndDate))
	}

	// 验证餐次类型
	if req.MealType != nil && !d.isValidMealType(*req.MealType) {
		return nil, errs.New(errs.CodeDietInvalidMealType, fmt.Sprintf("invalid meal type: %s, must be one of: breakfast, lunch, dinner, snacks", *req.MealType))
	}

	// 处理空字符串的餐次类型：将空字符串转换为nil，表示不筛选
	var mealTypeFilter *string
	if req.MealType != nil && *req.MealType != "" {
		mealTypeFilter = req.MealType
	}

	var dietRecords []*entities.DietRecord
	var total int64
	var err error

	// 根据是否指定用户ID选择不同的查询方法
	if req.UserID != nil && *req.UserID > 0 {
		// 查询指定用户的记录
		dietRecords, total, err = d.dietRecordRepo.SearchWithFoods(*req.UserID, startDate, endDate, mealTypeFilter, offset, size)
	} else {
		// 管理端跨用户查询：查询所有用户的记录
		dietRecords, total, err = d.dietRecordRepo.SearchAllUsersWithFoods(startDate, endDate, mealTypeFilter, offset, size)
	}

	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietInvalidParams, "failed to list diet records", err)
	}

	// 转换响应
	var responses []*v1.DietRecordResponse
	for _, record := range dietRecords {
		responses = append(responses, d.convertToDietRecordResponse(record))
	}

	return &v1.DietRecordListResponse{
		Total:   total,
		Records: responses,
		Current: page,
		Size:    size,
	}, nil
}

// DeleteDietRecord 删除饮食记录（管理端）
func (d *dietRecordAdminLogic) DeleteDietRecord(ctx context.Context, id int64) error {
	if id <= 0 {
		return errs.New(errs.CodeDietInvalidParams, "id must be positive")
	}

	// 检查记录是否存在
	_, err := d.dietRecordRepo.GetByID(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeDietRecordNotFound, "failed to check diet record existence", err)
	}

	// 删除饮食记录（级联删除食物明细）
	if err := d.dietRecordRepo.Delete(id); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeDietInvalidParams, "failed to delete diet record", err)
	}

	return nil
}

// BatchDeleteDietRecords 批量删除饮食记录（管理端）
func (d *dietRecordAdminLogic) BatchDeleteDietRecords(ctx context.Context, req *v1.DietRecordBatchDeleteReq) error {
	if len(req.IDs) == 0 {
		return errs.New(errs.CodeDietInvalidParams, "ids cannot be empty")
	}

	// 管理端不需要权限检查，直接批量删除
	if err := d.dietRecordRepo.BatchDelete(req.IDs); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeDietInvalidParams, "failed to batch delete diet records", err)
	}

	return nil
}

// GetNutritionStats 获取营养统计（管理端，支持跨用户统计）
func (d *dietRecordAdminLogic) GetNutritionStats(ctx context.Context, req *v1.NutritionStatsQueryReq) (*v1.NutritionStatsResponse, error) {
	// 解析日期范围
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		return nil, errs.New(errs.CodeDietInvalidParams, "invalid startDate format, expected YYYY-MM-DD")
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		return nil, errs.New(errs.CodeDietInvalidParams, "invalid endDate format, expected YYYY-MM-DD")
	}

	// 验证日期范围
	if startDate.After(endDate) {
		return nil, errs.New(errs.CodeDietInvalidDateRange, fmt.Sprintf("startDate %s cannot be after endDate %s", req.StartDate, req.EndDate))
	}

	// 验证餐次类型
	if req.MealType != nil && !d.isValidMealType(*req.MealType) {
		return nil, errs.New(errs.CodeDietInvalidMealType, fmt.Sprintf("invalid meal type: %s, must be one of: breakfast, lunch, dinner, snacks", *req.MealType))
	}

	// 管理端营养统计需要指定用户ID
	if req.UserID == nil || *req.UserID <= 0 {
		return nil, errs.New(errs.CodeDietInvalidParams, "userID is required for admin nutrition stats")
	}

	// 获取营养统计
	stats, err := d.dietRecordRepo.GetNutritionStatsByDateRange(*req.UserID, startDate, endDate, req.MealType)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietInvalidParams, "failed to get nutrition stats", err)
	}

	// 获取每日营养统计
	dailyStats, err := d.dietRecordRepo.GetDailyNutritionStats(*req.UserID, startDate, endDate)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietInvalidParams, "failed to get daily nutrition stats", err)
	}

	// 计算天数
	dayCount := int(endDate.Sub(startDate).Hours()/24) + 1

	// 计算平均值
	avgCalorie := 0.0
	avgProtein := 0.0
	avgFat := 0.0
	avgCarbs := 0.0
	if dayCount > 0 {
		avgCalorie = stats.TotalCalorie / float64(dayCount)
		avgProtein = stats.TotalProtein / float64(dayCount)
		avgFat = stats.TotalFat / float64(dayCount)
		avgCarbs = stats.TotalCarbs / float64(dayCount)
	}

	// 转换每日统计
	var dailyStatsResponse []v1.DailyNutritionStats
	for _, daily := range dailyStats {
		dailyStatsResponse = append(dailyStatsResponse, v1.DailyNutritionStats{
			Date:         daily.Date.Format("2006-01-02"),
			TotalCalorie: daily.TotalCalorie,
			TotalProtein: daily.TotalProtein,
			TotalFat:     daily.TotalFat,
			TotalCarbs:   daily.TotalCarbs,
			MealStats:    []v1.MealNutritionStats{}, // TODO: 实现餐次统计
		})
	}

	return &v1.NutritionStatsResponse{
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
		TotalCalorie: stats.TotalCalorie,
		TotalProtein: stats.TotalProtein,
		TotalFat:     stats.TotalFat,
		TotalCarbs:   stats.TotalCarbs,
		AvgCalorie:   avgCalorie,
		AvgProtein:   avgProtein,
		AvgFat:       avgFat,
		AvgCarbs:     avgCarbs,
		DayCount:     dayCount,
		DailyStats:   dailyStatsResponse,
	}, nil
}

// 验证餐次类型
func (d *dietRecordAdminLogic) isValidMealType(mealType string) bool {
	// 空字符串表示不筛选餐次类型，是有效的
	if mealType == "" {
		return true
	}
	validTypes := []string{"breakfast", "lunch", "dinner", "snacks"}
	for _, validType := range validTypes {
		if mealType == validType {
			return true
		}
	}
	return false
}

// 转换为饮食记录响应
func (d *dietRecordAdminLogic) convertToDietRecordResponse(dietRecord *entities.DietRecord) *v1.DietRecordResponse {
	response := &v1.DietRecordResponse{
		ID:           dietRecord.ID,
		UserID:       dietRecord.UserID,
		Username:     "", // TODO: 需要从用户服务获取用户名
		Date:         v1.DateOnly(dietRecord.Date),
		Time:         v1.TimeOnly(dietRecord.Time),
		MealType:     dietRecord.MealType,
		Remark:       dietRecord.Remark,
		TotalCalorie: dietRecord.TotalCalorie,
		CreatedAt:    dietRecord.CreatedAt,
		UpdatedAt:    dietRecord.UpdatedAt,
	}

	// 转换食物明细
	for _, food := range dietRecord.DietRecordFoods {
		foodResponse := v1.DietRecordFoodResponse{
			ID:           food.ID,
			DietRecordID: food.DietRecordID,
			FoodID:       food.FoodID,
			Name:         food.FoodName,
			Amount:       food.Amount,
			Unit:         food.Unit,
			Calories:     food.Calories,
			Protein:      food.Protein,
			Fat:          food.Fat,
			Carbs:        food.Carbs,
			Grams:        food.Grams,
			CreatedAt:    food.CreatedAt,
		}
		response.Foods = append(response.Foods, foodResponse)
	}

	return response
}
