package diet

import (
	"context"
	"errors"
	"fmt"
	"shikeyinxiang/internal/repositories/diet"
	"shikeyinxiang/internal/repositories/food"
	"time"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// dietRecordLogic 饮食记录业务逻辑实现
type dietRecordLogic struct {
	dietRecordRepo     diet.IDietRecordRepo
	dietRecordFoodRepo diet.IDietRecordFoodRepo
	foodRepo           food.IFoodRepo
}

// NewDietRecordLogic 创建饮食记录业务逻辑实例
func NewDietRecordLogic(
	dietRecordRepo diet.IDietRecordRepo,
	dietRecordFoodRepo diet.IDietRecordFoodRepo,
	foodRepo food.IFoodRepo,
) service.IDietRecordService {
	return &dietRecordLogic{
		dietRecordRepo:     dietRecordRepo,
		dietRecordFoodRepo: dietRecordFoodRepo,
		foodRepo:           foodRepo,
	}
}

// 确保 dietRecordLogic 实现了 IDietRecordService 接口
var _ service.IDietRecordService = &dietRecordLogic{}

// CreateDietRecord 创建饮食记录
func (d *dietRecordLogic) CreateDietRecord(ctx context.Context, userID int64, req *v1.DietRecordCreateReq) (bool, error) {

	// 解析日期和时间 - 使用本地时区
	date, err := time.ParseInLocation("2006-01-02", req.Date, time.Local)
	if err != nil {
		return false, errs.New(errs.CodeSystemInvalidParams, "invalid date format, expected YYYY-MM-DD")
	}

	// 尝试解析时间，支持 HH:MM 和 HH:MM:SS 格式
	timeValue, err := d.parseTimeWithFallback(req.Time)
	if err != nil {
		return false, errs.New(errs.CodeSystemInvalidParams, "invalid time format, expected HH:MM or HH:MM:SS")
	}

	// 创建饮食记录实体
	dietRecord := entities.NewDietRecord(userID, date, req.MealType)
	dietRecord.SetDateTime(date, timeValue)
	if req.Remark != "" {
		dietRecord.SetRemark(req.Remark)
	}

	// 创建食物明细实体
	var dietRecordFoods []*entities.DietRecordFood
	totalCalorie := 0.0

	for _, foodReq := range req.Foods {
		// 验证食物ID是否存在
		if _, err := d.foodRepo.GetByID(int(foodReq.FoodID)); err != nil {
			// 透传 Repository 层的 CodeError
			var codeErr *errs.CodeError
			if errors.As(err, &codeErr) {
				return false, err
			}
			return false, errs.Wrap(errs.CodeFoodNotFound, "failed to validate food", err)
		}

		// 创建食物明细
		dietRecordFood := entities.NewDietRecordFood(0, foodReq.FoodID, foodReq.Name, foodReq.Amount, foodReq.Unit)
		dietRecordFood.SetNutritionInfo(foodReq.Calories, foodReq.Protein, foodReq.Fat, foodReq.Carbs, foodReq.Grams)

		dietRecordFoods = append(dietRecordFoods, dietRecordFood)
		totalCalorie += foodReq.Calories
	}

	// 设置总热量
	dietRecord.TotalCalorie = totalCalorie

	// 创建饮食记录
	if err := d.dietRecordRepo.Create(dietRecord); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return false, err
		}
		return false, errs.Wrap(errs.CodeSystemInvalidParams, "failed to create diet record", err)
	}

	// 设置食物明细的饮食记录ID
	for _, food := range dietRecordFoods {
		food.DietRecordID = dietRecord.ID
	}

	// 批量创建食物明细
	if err := d.dietRecordFoodRepo.BatchCreate(dietRecordFoods); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return false, err
		}
		return false, errs.Wrap(errs.CodeSystemInvalidParams, "failed to create diet record foods", err)
	}

	return true, nil
}

// 管理端专用方法实现

// GetDietRecordDetail 获取饮食记录详情（不验证用户权限，管理端使用）
func (d *dietRecordLogic) GetDietRecordDetail(ctx context.Context, id int64) (*v1.DietRecordResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}

	// 获取饮食记录（管理端不需要权限检查）
	dietRecord, err := d.dietRecordRepo.GetByIDWithFoods(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietRecordNotFound, "failed to get diet record", err)
	}

	return d.convertToDietRecordResponse(dietRecord), nil
}

// GetDietRecord 获取饮食记录详情
func (d *dietRecordLogic) GetDietRecord(ctx context.Context, userID int64, id int64) (*v1.DietRecordResponse, error) {
	if id <= 0 {
		return nil, errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}

	// 获取饮食记录
	dietRecord, err := d.dietRecordRepo.GetByIDWithFoods(id)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeDietRecordNotFound, "failed to get diet record", err)
	}

	return d.convertToDietRecordResponse(dietRecord), nil
}

// DeleteDietRecord 删除饮食记录
func (d *dietRecordLogic) DeleteDietRecord(ctx context.Context, userID int64, id int64) error {
	if id <= 0 {
		return errs.New(errs.CodeSystemInvalidParams, "id must be positive")
	}

	// 删除饮食记录（级联删除食物明细）
	if err := d.dietRecordRepo.Delete(id); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeSystemInvalidParams, "failed to delete diet record", err)
	}

	return nil
}

// BatchDeleteDietRecords 批量删除饮食记录
func (d *dietRecordLogic) BatchDeleteDietRecords(ctx context.Context, userID int64, req *v1.DietRecordBatchDeleteReq) error {
	if len(req.IDs) == 0 {
		return errs.New(errs.CodeSystemInvalidParams, "ids cannot be empty")
	}

	// 批量删除
	if err := d.dietRecordRepo.BatchDelete(req.IDs); err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return err
		}
		return errs.Wrap(errs.CodeSystemInvalidParams, "failed to batch delete diet records", err)
	}

	return nil
}

// ListDietRecords 获取饮食记录列表
func (d *dietRecordLogic) ListDietRecords(ctx context.Context, userID int64, req *v1.DietRecordQueryReq) (*v1.DietRecordListResponse, error) {
	page := req.Current
	size := req.Size

	offset := (page - 1) * size

	// 解析日期范围 - 使用本地时区避免时区转换问题
	var startDate, endDate *time.Time
	if req.StartDate != nil {
		if parsed, err := time.ParseInLocation("2006-01-02", *req.StartDate, time.Local); err != nil {
			return nil, errs.New(errs.CodeSystemInvalidParams, "invalid startDate format, expected YYYY-MM-DD")
		} else {
			startDate = &parsed
		}
	}
	if req.EndDate != nil {
		if parsed, err := time.ParseInLocation("2006-01-02", *req.EndDate, time.Local); err != nil {
			return nil, errs.New(errs.CodeSystemInvalidParams, "invalid endDate format, expected YYYY-MM-DD")
		} else {
			endDate = &parsed
		}
	}

	// 验证日期范围
	if startDate != nil && endDate != nil && startDate.After(*endDate) {
		return nil, errs.New(errs.CodeDietInvalidDateRange, fmt.Sprintf("startDate %s cannot be after endDate %s", *req.StartDate, *req.EndDate))
	}

	// 处理空字符串的餐次类型：将空字符串转换为nil，表示不筛选
	var mealTypeFilter *string
	if req.MealType != nil && *req.MealType != "" {
		mealTypeFilter = req.MealType
	}

	// 查询数据
	dietRecords, total, err := d.dietRecordRepo.SearchWithFoods(userID, startDate, endDate, mealTypeFilter, offset, size)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to list diet records", err)
	}

	// 转换响应
	var responses []*v1.DietRecordResponse
	for _, record := range dietRecords {
		responses = append(responses, d.convertToDietRecordResponse(record))
	}

	return &v1.DietRecordListResponse{
		Total:   total,
		Records: responses,
		Current: page,
		Size:    size,
	}, nil
}

// GetDietRecordsByDate 获取指定日期的饮食记录
func (d *dietRecordLogic) GetDietRecordsByDate(ctx context.Context, userID int64, date string) ([]*v1.DietRecordResponse, error) {
	// 解析日期 - 使用本地时区
	parsedDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid date format, expected YYYY-MM-DD")
	}

	// 查询指定日期的记录
	dietRecords, err := d.dietRecordRepo.GetByDateWithFoods(userID, parsedDate)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to get diet records by date", err)
	}

	// 转换响应
	var responses []*v1.DietRecordResponse
	for _, record := range dietRecords {
		responses = append(responses, d.convertToDietRecordResponse(record))
	}

	return responses, nil
}

// 转换为饮食记录响应
func (d *dietRecordLogic) convertToDietRecordResponse(dietRecord *entities.DietRecord) *v1.DietRecordResponse {
	response := &v1.DietRecordResponse{
		ID:           dietRecord.ID,
		UserID:       dietRecord.UserID,
		Date:         v1.DateOnly(dietRecord.Date),
		Time:         v1.TimeOnly(dietRecord.Time),
		MealType:     dietRecord.MealType,
		Remark:       dietRecord.Remark,
		TotalCalorie: dietRecord.TotalCalorie,
		CreatedAt:    dietRecord.CreatedAt,
		UpdatedAt:    dietRecord.UpdatedAt,
	}

	// 转换食物明细
	for _, food := range dietRecord.DietRecordFoods {
		foodResponse := v1.DietRecordFoodResponse{
			ID:           food.ID,
			DietRecordID: food.DietRecordID,
			FoodID:       food.FoodID,
			Name:         food.FoodName,
			Amount:       food.Amount,
			Unit:         food.Unit,
			Calories:     food.Calories,
			Protein:      food.Protein,
			Fat:          food.Fat,
			Carbs:        food.Carbs,
			Grams:        food.Grams,
			CreatedAt:    food.CreatedAt,
		}
		response.Foods = append(response.Foods, foodResponse)
	}

	return response
}

// GetBatchDietRecordsForNutritionStat 批量获取多个用户在指定日期范围内的饮食记录
// 专门用于营养统计的聚合查询，避免多次查询
func (d *dietRecordLogic) GetBatchDietRecordsForNutritionStat(ctx context.Context, userIDs []int64, startDate, endDate string) (map[int64]map[string][]*v1.DietRecordResponse, error) {
	if len(userIDs) == 0 {
		return make(map[int64]map[string][]*v1.DietRecordResponse), nil
	}

	// 解析日期 - 使用本地时区
	start, err := time.ParseInLocation("2006-01-02", startDate, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid startDate format, expected YYYY-MM-DD")
	}

	end, err := time.ParseInLocation("2006-01-02", endDate, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid endDate format, expected YYYY-MM-DD")
	}

	// 调用repository获取批量数据
	batchRecords, err := d.dietRecordRepo.GetBatchDietRecordsForNutritionStat(userIDs, start, end)
	if err != nil {
		// 透传 Repository 层的 CodeError
		var codeErr *errs.CodeError
		if errors.As(err, &codeErr) {
			return nil, err
		}
		return nil, errs.Wrap(errs.CodeSystemInvalidParams, "failed to get batch diet records for nutrition stat", err)
	}

	// 转换为响应格式
	result := make(map[int64]map[string][]*v1.DietRecordResponse)

	for userID, userRecords := range batchRecords {
		result[userID] = make(map[string][]*v1.DietRecordResponse)

		for dateStr, records := range userRecords {
			var responses []*v1.DietRecordResponse
			for _, record := range records {
				responses = append(responses, d.convertToDietRecordResponse(record))
			}
			result[userID][dateStr] = responses
		}
	}

	return result, nil
}

// FindActiveUserIdsByDate 获取指定日期有饮食记录的活跃用户ID列表
func (d *dietRecordLogic) FindActiveUserIdsByDate(ctx context.Context, date string) ([]int64, error) {
	// 解析日期 - 使用本地时区
	parsedDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid date format, expected YYYY-MM-DD")
	}

	// 调用repository获取活跃用户ID列表
	userIDs, err := d.dietRecordRepo.FindActiveUserIdsByDate(parsedDate)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to find active user ids by date: %w", err)
	}

	return userIDs, nil
}

// FindActiveUserIdsByDateRange 获取指定日期范围内有饮食记录的活跃用户ID列表
func (d *dietRecordLogic) FindActiveUserIdsByDateRange(ctx context.Context, startDate, endDate string) ([]int64, error) {
	// 解析日期 - 使用本地时区
	start, err := time.ParseInLocation("2006-01-02", startDate, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid startDate format, expected YYYY-MM-DD")
	}

	end, err := time.ParseInLocation("2006-01-02", endDate, time.Local)
	if err != nil {
		return nil, errs.New(errs.CodeSystemInvalidParams, "invalid endDate format, expected YYYY-MM-DD")
	}

	// 调用repository获取活跃用户ID列表
	userIDs, err := d.dietRecordRepo.FindActiveUserIdsByDateRange(start, end)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to find active user ids by date range: %w", err)
	}

	return userIDs, nil
}

// CountDietRecordsByDate 统计指定日期的饮食记录数量
func (d *dietRecordLogic) CountDietRecordsByDate(ctx context.Context, date string) (int, error) {
	// 解析日期 - 使用本地时区避免时区转换问题
	parsedDate, err := time.ParseInLocation("2006-01-02", date, time.Local)
	if err != nil {
		return 0, errs.New(errs.CodeSystemInvalidParams, "invalid date format, expected YYYY-MM-DD")
	}

	// 添加调试日志
	fmt.Printf("DEBUG: CountDietRecordsByDate - date=%s, parsedDate=%v\n", date, parsedDate)

	// 调用repository统计记录数量
	count, err := d.dietRecordRepo.CountByDate(parsedDate)
	if err != nil {
		return 0, fmt.Errorf("logic: failed to count diet records by date: %w", err)
	}

	fmt.Printf("DEBUG: CountDietRecordsByDate - count=%d\n", count)
	return count, nil
}

// GetPopularFoodsByPeriod 根据时间周期获取热门食物统计
func (d *dietRecordLogic) GetPopularFoodsByPeriod(ctx context.Context, period string, limit int) ([]map[string]interface{}, error) {
	// 参数验证
	if limit <= 0 {
		return nil, errs.New(errs.CodeSystemInvalidParams, "limit must be positive")
	}

	// 根据时间周期确定日期范围
	endDate := time.Now()
	var startDate time.Time

	switch period {
	case "week":
		startDate = endDate.AddDate(0, 0, -7) // 1周前
	case "quarter":
		startDate = endDate.AddDate(0, -3, 0) // 3个月前
	case "month":
		startDate = endDate.AddDate(0, -1, 0) // 1个月前
	default:
		// 默认为month
		startDate = endDate.AddDate(0, -1, 0)
	}

	// 调用repository查询热门食物
	popularFoods, err := d.dietRecordRepo.FindPopularFoodsByPeriod(startDate, endDate, limit)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get popular foods by period: %w", err)
	}

	// 如果结果为空，返回空数组而不是nil
	if popularFoods == nil {
		return make([]map[string]interface{}, 0), nil
	}

	return popularFoods, nil
}

// parseTimeWithFallback 解析时间，支持 HH:MM 和 HH:MM:SS 格式
func (d *dietRecordLogic) parseTimeWithFallback(timeStr string) (time.Time, error) {
	// 直接解析时间部分，不需要日期
	// 首先尝试 HH:MM:SS 格式
	if timeValue, err := time.Parse("15:04:05", timeStr); err == nil {
		return timeValue, nil
	}

	// 如果失败，尝试 HH:MM 格式
	if timeValue, err := time.Parse("15:04", timeStr); err == nil {
		return timeValue, nil
	}

	return time.Time{}, fmt.Errorf("invalid time format")
}
