package controllers

import (
	"log"
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/pkg/errs"
	"shikeyinxiang/internal/service"
)

// DietRecordController 饮食记录控制器，处理用户端的饮食记录相关HTTP请求
type DietRecordController struct {
	dietRecordSvc service.IDietRecordService
}

// NewDietRecordController 创建一个新的 DietRecordController 实例
func NewDietRecordController(dietRecordSvc service.IDietRecordService) *DietRecordController {
	return &DietRecordController{
		dietRecordSvc: dietRecordSvc,
	}
}

// bindJSONAndValidate 统一处理JSON参数绑定和验证
func (dc *DietRecordController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		// 记录详细的参数绑定错误
		log.Printf("JSON binding error for %s %s: %v", c.Request.Method, c.Request.URL.Path, err)
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid request parameters"))
		return false
	}
	return true
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (dc *DietRecordController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid query parameters"))
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (dc *DietRecordController) parseIDParam(c *gin.Context, paramName string) (int64, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		c.Error(errs.New(errs.CodeDietInvalidParams, "missing id parameter"))
		return 0, false
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil || id <= 0 {
		c.Error(errs.New(errs.CodeDietInvalidParams, "invalid id parameter"))
		return 0, false
	}

	return id, true
}

// getCurrentUserID 获取当前用户ID
func (dc *DietRecordController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		c.Error(errs.New(errs.CodeAuthTokenInvalid, "authentication required"))
		return 0, false
	}
	return userID, true
}

// CreateDietRecord 创建饮食记录
func (dc *DietRecordController) CreateDietRecord(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req v1.DietRecordCreateReq
	if !dc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.CreateDietRecord(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetDietRecord 获取饮食记录详情
func (dc *DietRecordController) GetDietRecord(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 解析ID参数
	id, ok := dc.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.GetDietRecord(c.Request.Context(), userID, id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateDietRecord 更新饮食记录
func (dc *DietRecordController) UpdateDietRecord(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req v1.DietRecordUpdateReq
	if !dc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.UpdateDietRecord(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteDietRecord 删除饮食记录
func (dc *DietRecordController) DeleteDietRecord(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 解析ID参数
	id, ok := dc.parseIDParam(c, "id")
	if !ok {
		return
	}

	// 调用服务层
	err := dc.dietRecordSvc.DeleteDietRecord(c.Request.Context(), userID, id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, nil)
}

// BatchDeleteDietRecords 批量删除饮食记录
func (dc *DietRecordController) BatchDeleteDietRecords(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定请求参数
	var req v1.DietRecordBatchDeleteReq
	if !dc.bindJSONAndValidate(c, &req) {
		return
	}

	// 调用服务层
	err := dc.dietRecordSvc.BatchDeleteDietRecords(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, nil)
}

// GetDietRecordList 获取饮食记录列表（支持分页、筛选）
func (dc *DietRecordController) GetDietRecordList(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.DietRecordQueryReq
	if !dc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.ListDietRecords(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// SearchDietRecords 搜索饮食记录
func (dc *DietRecordController) SearchDietRecords(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.DietRecordQueryReq
	if !dc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.SearchDietRecords(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetDietRecordsByDate 获取指定日期的饮食记录
func (dc *DietRecordController) GetDietRecordsByDate(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 获取日期参数
	date := c.Param("date")
	if date == "" {
		c.Error(errs.New(errs.CodeDietInvalidParams, "date parameter is required"))
		return
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.GetDietRecordsByDate(c.Request.Context(), userID, date)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// GetNutritionStats 获取营养统计
func (dc *DietRecordController) GetNutritionStats(c *gin.Context) {
	// 获取当前用户ID
	userID, ok := dc.getCurrentUserID(c)
	if !ok {
		return
	}

	// 绑定查询参数
	var req v1.NutritionStatsQueryReq
	if !dc.bindQueryAndValidate(c, &req) {
		return
	}

	// 调用服务层
	res, err := dc.dietRecordSvc.GetNutritionStats(c.Request.Context(), userID, &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}
